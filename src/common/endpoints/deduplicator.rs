use std::collections::HashSet;
use std::sync::Mutex;
use xxhash_rust::xxh3::xxh3_64;

pub struct EntryDeduplicator {
    inner: Mutex<DeduplicatorInner>,
}

struct DeduplicatorInner {
    seen_hashes: HashSet<u64>,
    current_slot: u64,
}

impl EntryDeduplicator {
    pub fn new() -> Self {
        Self { inner: Mutex::new(DeduplicatorInner { seen_hashes: HashSet::new(), current_slot: 0 }) }
    }

    pub fn should_process(&self, slot: u64, entries_data: &[u8]) -> bool {
        let mut inner = self.inner.lock().unwrap();

        if slot < inner.current_slot {
            return false;
        }

        if slot > inner.current_slot {
            inner.current_slot = slot;
            inner.seen_hashes.clear();
        }

        let hash = xxh3_64(entries_data);
        inner.seen_hashes.insert(hash)
    }
}
